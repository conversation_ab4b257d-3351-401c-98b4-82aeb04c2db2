const request = require('supertest');
const app = require('../../src/app');
const { User, UserProfile, School } = require('../../src/models');
const jwt = require('jsonwebtoken');

describe('UserController - updateProfile', () => {
  let testUser;
  let authToken;
  let testSchool;

  beforeEach(async () => {
    // Create test user
    testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      password_hash: 'hashedpassword',
      user_type: 'user',
      is_active: true
    });

    // Create test school
    testSchool = await School.create({
      name: 'Test School',
      city: 'Test City',
      province: 'Test Province'
    });

    // Generate auth token
    authToken = jwt.sign(
      { id: testUser.id, email: testUser.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterEach(async () => {
    // Clean up
    await UserProfile.destroy({ where: { user_id: testUser.id } });
    await User.destroy({ where: { id: testUser.id } });
    await School.destroy({ where: { id: testSchool.id } });
  });

  describe('school_id validation', () => {
    it('should successfully update profile with valid school_id', async () => {
      const response = await request(app)
        .put('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          full_name: 'Test User',
          school_id: testSchool.id,
          gender: 'male'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.school_id).toBe(testSchool.id);
    });

    it('should reject update with invalid school_id', async () => {
      const invalidSchoolId = 99999;
      
      const response = await request(app)
        .put('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          full_name: 'Test User',
          school_id: invalidSchoolId,
          gender: 'male'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_SCHOOL_ID');
      expect(response.body.error.message).toContain(`School with ID ${invalidSchoolId} does not exist`);
    });



    it('should handle null school_id gracefully', async () => {
      const response = await request(app)
        .put('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          full_name: 'Test User',
          school_id: null,
          gender: 'male'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.profile.school_id).toBeNull();
    });
  });
});
